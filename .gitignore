# Python
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
.venv/
ENV/
.env.bak/

# pytest
.cache/

# mypy
.mypy_cache/
.dmypy.json

# coverage
.coverage
.coverage.*
htmlcov/

# Jupyter Notebook
.ipynb_checkpoints

# PyInstaller
*.manifest
*.spec

# Logs
logs/
*.log

# macOS
.DS_Store

# IDEs
.idea/
.vscode/

# Docker artifacts
*.pid
docker-compose.override.yml

# Environment variables
.env

# Sentry release file
.sentry-release

# Others
*.sqlite3
