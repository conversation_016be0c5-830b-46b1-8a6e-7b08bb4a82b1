-- create read-only user
CREATE USER read_user WITH PASSWORD 'read_pass';

-- grant read-only access
GRANT CONNECT ON DATABASE oms_db TO read_user;

\c oms_db

GRANT USAGE ON SCHEMA public TO read_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO read_user;

-- future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO read_user;

-- allow replication role
ALTER ROLE ${WRITE_DB_USER} WITH REPLICATION;
