#!/bin/bash
set -e

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" <<-EOSQL
  CREATE USER ${READ_DB_USER} WITH PASSWORD '${READ_DB_PASSWORD}';
  GRANT CONNECT ON DATABASE ${POST<PERSON>ES_DB} TO ${READ_DB_USER};

  \connect ${POSTGRES_DB}
  GRANT USAGE ON SCHEMA public TO ${READ_DB_USER};
  GRANT SELECT ON ALL TABLES IN SCHEMA public TO ${READ_DB_USER};
  ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO ${READ_DB_USER};

  ALTER ROLE ${WRITE_DB_USER} WITH REPLICATION;
EOSQL
