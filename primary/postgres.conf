# Connection settings
listen_addresses = '*'
port = 5432

# Replication settings
wal_level = replica
max_wal_senders = 10
wal_keep_size = 64MB
max_replication_slots = 10

# Performance settings
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Logging
log_destination = 'stderr'
logging_collector = on
log_statement = 'all'
log_min_duration_statement = 1000

# Authentication
hba_file = '/var/lib/postgresql/data/pg_hba.conf'
