# Example environment variables for Rozana OMS Service
WRITE_DATABASE_URL=postgresql://<user>:<password>@postgres-db:5432/oms_db
READ_DATABASE_URL=postgresql://<user>:<password>@postgres-db:5432/oms_db
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0
KAFKA_BOOTSTRAP_SERVERS=kafka:9092
KAFKA_ORDER_TOPIC=order-events
KAFKA_CONSUMER_GROUP=order-processor
IMS_BASE_URL=https://ims.example.com
SENTRY_DSN=http://<EMAIL>/2
ENVIRONMENT=development

POSTGRES_USER=<user>
POSTGRES_PASSWORD=<password>
POSTGRES_DB=oms_db

ZOOKEEPER_CLIENT_PORT=2181
ZOOKEEPER_TICK_TIME=2000

KAFKA_BROKER_ID=1
KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181
KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092
KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
KAFKA_AUTO_CREATE_TOPICS_ENABLE=true
