services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: oms-api
    ports:
      - "8000:8000"
    env_file:
      - .env
    depends_on:
      - postgres-db
      - redis
      - kafka
    command: >
      bash -c "
        echo 'Waiting for databases to be ready...' &&
        sleep 10 &&
        python db_setup.py &&
        uvicorn main:app --host 0.0.0.0 --port 8000
      "
    networks:
      - oms-network
    volumes:
      - ./app:/app

  consumer:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: oms-consumer
    env_file:
      - .env
    depends_on:
      - postgres-db
      - kafka
    command: >
      bash -c "
        echo 'Waiting for services to be ready...' &&
        sleep 15 &&
        python consumer.py
      "
    networks:
      - oms-network
    volumes:
      - ./app:/app

  postgres-db:
    image: postgres:15
    container_name: oms-postgres
    environment:
      POSTGRES_USER: ${WRITE_DB_USER}
      POSTGRES_PASSWORD: ${WRITE_DB_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./primary/postgres.conf:/etc/postgresql/postgresql.conf
      - ./primary/pg_hba.conf:/etc/postgresql/pg_hba.conf
      - ./primary/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - oms-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${WRITE_DB_USER} -d ${POSTGRES_DB}"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: postgres -c config_file=/etc/postgresql/postgresql.conf -c hba_file=/etc/postgresql/pg_hba.conf

  postgres-read-db:
    image: postgres:15
    container_name: oms-postgres-replica
    environment:
      POSTGRES_USER: ${READ_DB_USER}
      POSTGRES_PASSWORD: ${READ_DB_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      PGUSER: ${READ_DB_USER}
    depends_on:
      postgres-db:
        condition: service_healthy
    ports:
      - "5433:5432"
    networks:
      - oms-network
    volumes:
      - postgres-read-data:/var/lib/postgresql/data
      - ./replica/postgres.conf:/etc/postgresql/postgresql.conf
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${READ_DB_USER} -d ${POSTGRES_DB}"]
      interval: 15s
      timeout: 10s
      retries: 10
      start_period: 30s
    command: >
      bash -c "
        echo 'Waiting for primary database to be ready...' &&
        until pg_isready -h postgres-db -U ${WRITE_DB_USER} -d ${POSTGRES_DB}; do
          echo 'Primary database not ready, waiting...'
          sleep 2
        done &&
        echo 'Primary database is ready, setting up replica...' &&
        rm -rf /var/lib/postgresql/data/* &&
        PGPASSWORD=${WRITE_DB_PASSWORD} pg_basebackup -h postgres-db -U ${WRITE_DB_USER} -D /var/lib/postgresql/data -Fp -Xs -P -R -S replica_slot &&
        chown -R postgres:postgres /var/lib/postgresql/data &&
        chmod 700 /var/lib/postgresql/data &&
        exec gosu postgres postgres -c config_file=/etc/postgresql/postgresql.conf
      "
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: oms-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - oms-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    command: redis-server --appendonly yes

  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: oms-zookeeper
    env_file:
      - .env
    networks:
      - oms-network
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "2181"]
      interval: 10s
      timeout: 5s
      retries: 5

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: oms-kafka
    depends_on:
      zookeeper:
        condition: service_healthy
    ports:
      - "9092:9092"
    env_file:
      - .env
    networks:
      - oms-network
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  postgres-data:
  postgres-read-data:
  redis-data:

networks:
  oms-network:
    name: oms-network
    driver: bridge