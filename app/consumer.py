import asyncio
import logging
import os
from typing import Dict, Any
from infrastructure import kafka_manager
from database import get_write_pool
import psycopg
from datetime import datetime
from dotenv import load_dotenv

load_dotenv()

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialise Sentry (does nothing if SENTRY_DSN is not set)
import sentry_setup  # noqa: F401

class OrderEventProcessor:
    """Process order events from Kafka and write to master database"""
    
    def __init__(self):
        self.write_pool = None
    
    def get_write_connection(self):
        """Get write database connection"""
        if not self.write_pool:
            self.write_pool = get_write_pool()
        return self.write_pool.connection()
    
    async def process_order_created(self, event_data: Dict[str, Any]):
        """Process order creation event"""
        try:
            with self.get_write_connection() as conn:
                with conn.cursor() as cursor:
                    # Insert order
                    order_query = """
                        INSERT INTO orders (
                            order_id, customer_id, facility_id, status,
                            total_amount, order_datetime, eta
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (order_id) DO UPDATE SET
                            customer_id = EXCLUDED.customer_id,
                            facility_id = EXCLUDED.facility_id,
                            status = EXCLUDED.status,
                            total_amount = EXCLUDED.total_amount,
                            order_datetime = EXCLUDED.order_datetime,
                            eta = EXCLUDED.eta,
                            updated_at = CURRENT_TIMESTAMP
                        RETURNING id
                    """
                    
                    order_values = (
                        event_data['order_id'],
                        event_data['customer_id'],
                        event_data['facility_id'],
                        event_data['status'],
                        event_data['total_amount'],
                        event_data['order_datetime'],
                        event_data.get('eta')
                    )
                    
                    cursor.execute(order_query, order_values)
                    
                    # Insert order items
                    if 'items' in event_data and event_data['items']:
                        # First, delete existing items for this order
                        cursor.execute("DELETE FROM order_items WHERE order_id = %s", (event_data['order_id'],))
                        
                        item_query = """
                            INSERT INTO order_items (
                                order_id, sku, quantity, unit_price, sale_price
                            ) VALUES (%s, %s, %s, %s, %s)
                        """
                        
                        for item in event_data['items']:
                            item_values = (
                                event_data['order_id'],
                                item['sku'],
                                item['quantity'],
                                item['unit_price'],
                                item['sale_price']
                            )
                            cursor.execute(item_query, item_values)
                    
                    # Add to order journey
                    journey_query = """
                        INSERT INTO order_journey (order_id, current_status, previous_status)
                        VALUES (%s, %s, %s)
                    """
                    cursor.execute(journey_query, (event_data['order_id'], event_data['status'], None))
                    
                    conn.commit()
                    logger.info(f"Order {event_data['order_id']} created in database")
                    
        except Exception as e:
            logger.error(f"Failed to process order creation for {event_data.get('order_id')}: {e}")
            raise
    
    async def process_order_updated(self, event_data: Dict[str, Any]):
        """Process order update event"""
        try:
            with self.get_write_connection() as conn:
                with conn.cursor() as cursor:
                    # Get current status for journey tracking
                    cursor.execute("SELECT status FROM orders WHERE order_id = %s", (event_data['order_id'],))
                    current_row = cursor.fetchone()
                    previous_status = current_row[0] if current_row else None
                    
                    # Build dynamic update query based on provided fields
                    update_fields = []
                    update_values = []
                    
                    for field in ['customer_id', 'facility_id', 'status', 'total_amount', 'eta']:
                        if field in event_data:
                            update_fields.append(f"{field} = %s")
                            update_values.append(event_data[field])
                    
                    if update_fields:
                        update_fields.append("updated_at = CURRENT_TIMESTAMP")
                        update_values.append(event_data['order_id'])
                        
                        update_query = f"""
                            UPDATE orders SET {', '.join(update_fields)}
                            WHERE order_id = %s
                            RETURNING id
                        """
                        
                        cursor.execute(update_query, update_values)
                        
                        if not cursor.fetchone():
                            logger.warning(f"Order {event_data['order_id']} not found for update")
                            return
                    
                    # Update order items if provided
                    if 'items' in event_data and event_data['items']:
                        # Delete existing items
                        cursor.execute("DELETE FROM order_items WHERE order_id = %s", (event_data['order_id'],))
                        
                        # Insert updated items
                        item_query = """
                            INSERT INTO order_items (
                                order_id, sku, quantity, unit_price, sale_price
                            ) VALUES (%s, %s, %s, %s, %s)
                        """
                        
                        for item in event_data['items']:
                            item_values = (
                                event_data['order_id'],
                                item['sku'],
                                item['quantity'],
                                item['unit_price'],
                                item['sale_price']
                            )
                            cursor.execute(item_query, item_values)
                    
                    # Add to order journey if status changed
                    if 'status' in event_data and event_data['status'] != previous_status:
                        journey_query = """
                            INSERT INTO order_journey (order_id, current_status, previous_status)
                            VALUES (%s, %s, %s)
                        """
                        cursor.execute(journey_query, (event_data['order_id'], event_data['status'], previous_status))
                    
                    conn.commit()
                    logger.info(f"Order {event_data['order_id']} updated in database")
                    
        except Exception as e:
            logger.error(f"Failed to process order update for {event_data.get('order_id')}: {e}")
            raise
    
    async def process_event(self, event: Dict[str, Any]):
        """Process a single event based on event type"""
        event_type = event.get('event_type')
        event_data = event.get('data', {})
        
        try:
            if event_type == 'order_created':
                await self.process_order_created(event_data)
            elif event_type == 'order_updated':
                await self.process_order_updated(event_data)
            else:
                logger.warning(f"Unknown event type: {event_type}")
                
        except Exception as e:
            logger.error(f"Failed to process event {event_type}: {e}")
            # In a production system, you might want to send to a dead letter queue
            raise

class OrderEventConsumer:
    """Kafka consumer for order events"""
    
    def __init__(self):
        self.processor = OrderEventProcessor()
        self.consumer = None
        self.running = False
    
    async def start_consuming(self):
        """Start consuming order events from Kafka"""
        topic = os.getenv("KAFKA_ORDER_TOPIC", "order-events")
        group_id = os.getenv("KAFKA_CONSUMER_GROUP", "order-processor")
        
        try:
            self.consumer = await kafka_manager.create_consumer(topic, group_id)
            self.running = True
            
            logger.info(f"Started consuming from topic {topic}")
            
            async for message in self.consumer:
                if not self.running:
                    break
                
                try:
                    event = message.value
                    logger.info(f"Processing event: {event.get('event_type')} for order {event.get('data', {}).get('order_id')}")
                    
                    await self.processor.process_event(event)
                    
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    # Continue processing other messages
                    continue
                    
        except Exception as e:
            logger.error(f"Error in consumer: {e}")
            raise
        finally:
            if self.consumer:
                await self.consumer.stop()
    
    async def stop_consuming(self):
        """Stop consuming events"""
        self.running = False
        if self.consumer:
            await self.consumer.stop()
        logger.info("Consumer stopped")

async def main():
    """Main function to run the consumer"""
    consumer = OrderEventConsumer()
    
    try:
        await consumer.start_consuming()
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    finally:
        await consumer.stop_consuming()

if __name__ == "__main__":
    asyncio.run(main())
