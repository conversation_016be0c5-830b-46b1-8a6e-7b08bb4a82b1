"""Centralized Sentry initialization for the OMS service.

Importing this module will configure Sentry if the `SENTRY_DSN` environment
variable is present. The configuration is safe to import multiple times across
processes or modules.
"""
from __future__ import annotations

import logging
import os
from typing import Optional

import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration
from sentry_sdk.integrations.fastapi import FastApiIntegration
from sentry_sdk.integrations.starlette import StarletteIntegration

logger = logging.getLogger(__name__)


def init_sentry() -> Optional[bool]:
    """Initialise Sentry SDK if a DSN is configured.

    Returns True if initialised, False if DSN is missing.
    The function is idempotent – repeated calls will be ignored by sentry-sdk.
    """
    dsn = os.getenv("SENTRY_DSN")
    if not dsn:
        logger.info("SENTRY_DSN not set; Sentry will not be initialised.")
        return False

    # Configure logging breadcrumb and event levels
    logging_integration = LoggingIntegration(
        level=logging.INFO,        # Capture INFO and above as breadcrumbs
        event_level=logging.ERROR,  # Send events at ERROR and above
    )

    sentry_sdk.init(
        dsn=dsn,
        traces_sample_rate=float(os.getenv("SENTRY_TRACES_SAMPLE_RATE", "0.1")),
        environment=os.getenv("ENVIRONMENT", "development"),
        integrations=[
            StarletteIntegration(
                transaction_style="endpoint",
            ),
            FastApiIntegration(
                transaction_style="endpoint",
            ),
            logging_integration
        ]
    )

    logger.info("Sentry successfully initialised.")
    return True


# Automatically initialise on import so that `import sentry_setup` is enough.
init_sentry()
