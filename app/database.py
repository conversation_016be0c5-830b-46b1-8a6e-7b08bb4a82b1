from psycopg_pool import ConnectionPool
from contextlib import asynccontextmanager
import os
import logging
from dotenv import load_dotenv

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

write_db_pool = None
read_db_pool = None

def get_write_pool():
    global write_db_pool
    if write_db_pool is None:
        try:
            write_db_pool = ConnectionPool(
                conninfo=os.getenv("WRITE_DATABASE_URL"),
                min_size=2,
                max_size=20,
                kwargs={
                    "keepalives_idle": 600,
                    "keepalives_interval": 30,
                    "keepalives_count": 3
                }
            )
            logger.info("Write database connection pool initialized")
        except Exception as e:
            logger.error(f"Failed to initialize write database pool: {e}")
            raise
    return write_db_pool

def get_read_pool():
    global read_db_pool
    if read_db_pool is None:
        try:
            read_db_pool = ConnectionPool(
                conninfo=os.getenv("READ_DATABASE_URL"),
                min_size=3,  
                max_size=30, 
                kwargs={
                    "keepalives_idle": 600,
                    "keepalives_interval": 30,
                    "keepalives_count": 3
                }
            )
            logger.info("Read database connection pool initialized")
        except Exception as e:
            logger.error(f"Failed to initialize read database pool: {e}")
            raise
    return read_db_pool

@asynccontextmanager
async def get_write_db():
    pool = get_write_pool()
    try:
        with pool.connection() as conn:
            conn.autocommit = False
            logger.debug("Write database connection acquired")
            yield conn
    except Exception as e:
        logger.error(f"Error with write database connection: {e}")
        raise

@asynccontextmanager
async def get_read_db():
    pool = get_read_pool()
    try:
        with pool.connection() as conn:
            conn.autocommit = True
            logger.debug("Read database connection acquired")
            yield conn
    except Exception as e:
        logger.error(f"Error with read database connection: {e}")
        raise

def close_all_pools():
    """Close all database connection pools - useful for cleanup"""
    global write_db_pool, read_db_pool

    if write_db_pool:
        try:
            write_db_pool.close()
            logger.info("Write database pool closed")
        except Exception as e:
            logger.error(f"Error closing write database pool: {e}")
        finally:
            write_db_pool = None

    if read_db_pool:
        try:
            read_db_pool.close()
            logger.info("Read database pool closed")
        except Exception as e:
            logger.error(f"Error closing read database pool: {e}")
        finally:
            read_db_pool = None