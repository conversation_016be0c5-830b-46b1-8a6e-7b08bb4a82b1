from typing import List, Dict, Optional, Union
import logging
from datetime import datetime
from infrastructure import redis_manager, kafka_manager
from ims_service import ims_service

logger = logging.getLogger(__name__)

class OrderCommandService:
    """Service for handling order commands (Create, Update) with <PERSON><PERSON> and <PERSON>fka"""

    def __init__(self, write_db=None):
        self.write_db = write_db

    async def create_order(self, order_data: Dict) -> Dict:
        """Create order - Check inventory, then cache in Redis and publish to Kafka"""
        try:
            # Step 1: Check inventory availability
            logger.info(f"Checking inventory for order {order_data['order_id']}")

            stock_check = await ims_service.check_stock_availability(
                facility_id=order_data["facility_id"],
                items=order_data["items"]
            )

            # Step 2: If inventory is not available, return stock details
            if not stock_check["all_available"]:
                logger.warning(f"Insufficient inventory for order {order_data['order_id']}")
                return {
                    "success": False,
                    "message": "Insufficient inventory for one or more items",
                    "order_id": order_data["order_id"],
                    "inventory_check": stock_check
                }

            # Step 3: All items available - proceed with order creation
            logger.info(f"Inventory check passed for order {order_data['order_id']}")

            # Prepare order data for caching
            cached_order = {
                "order_id": order_data["order_id"],
                "customer_id": order_data["customer_id"],
                "facility_id": order_data["facility_id"],
                "status": order_data["status"],
                "total_amount": order_data["total_amount"],
                "order_datetime": str(order_data["order_datetime"]),
                "eta": str(order_data.get("eta")) if order_data.get("eta") else None,
                "items": order_data["items"],
                "created_at": str(datetime.now()),
                "updated_at": str(datetime.now()),
                "inventory_checked": True,
                "inventory_check_time": str(datetime.now())
            }

            # Cache in Redis
            redis_manager.set_order_cache(order_data["order_id"], cached_order)

            # Publish to Kafka for async processing
            await kafka_manager.publish_order_event("order_created", cached_order)

            logger.info(f"Order {order_data['order_id']} cached and event published")

            return {
                "success": True,
                "message": "Order created successfully",
                "order_id": order_data["order_id"],
                "eta": order_data.get("eta"),
                "inventory_check": stock_check
            }

        except Exception as e:
            logger.error(f"Failed to create order {order_data.get('order_id')}: {e}")
            # If it's an inventory service error, return detailed error
            if "inventory" in str(e).lower() or "ims" in str(e).lower():
                return {
                    "success": False,
                    "message": f"Inventory check failed: {str(e)}",
                    "order_id": order_data.get("order_id"),
                    "error_type": "inventory_service_error"
                }
            raise

    async def update_order(self, order_id: str, update_data: Dict) -> Dict:
        """Update order - Cache in Redis and publish to Kafka"""
        try:
            # Get existing cached order or create new cache entry
            cached_order = redis_manager.get_order_cache(order_id)
            if not cached_order:
                # If not in cache, we'll create a minimal cache entry for the update
                cached_order = {
                    "order_id": order_id,
                    "updated_at": str(datetime.now())
                }

            # Update cached data
            cached_order.update(update_data)
            cached_order["updated_at"] = str(datetime.now())

            # Cache updated order
            redis_manager.set_order_cache(order_id, cached_order)

            # Publish to Kafka for async processing
            await kafka_manager.publish_order_event("order_updated", cached_order)

            logger.info(f"Order {order_id} updated in cache and event published")

            return {"message": "Order updated successfully"}

        except Exception as e:
            logger.error(f"Failed to update order {order_id}: {e}")
            raise

    def cancel_order(self, order_id: str) -> Dict:
        """Cancel order - Direct write to master database (synchronous)"""
        if not self.write_db:
            raise ValueError("Write database is required for cancel operation")

        with self.write_db.cursor() as cursor:
            try:
                # Update order status to cancelled
                query = """
                    UPDATE orders SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
                    WHERE order_id = %s
                    RETURNING id, order_id
                """
                cursor.execute(query, (order_id,))
                result = cursor.fetchone()

                if not result:
                    raise ValueError("Order not found")

                # Add to order journey
                journey_query = """
                    INSERT INTO order_journey (order_id, current_status, previous_status)
                    SELECT %s, 'cancelled', status FROM orders WHERE order_id = %s
                """
                cursor.execute(journey_query, (order_id, order_id))

                self.write_db.commit()

                # Remove from Redis cache if exists
                redis_manager.delete_order_cache(order_id)

                logger.info(f"Order {order_id} cancelled successfully")
                return {"message": "Order cancelled successfully"}

            except Exception:
                self.write_db.rollback()
                raise

class OrderQueryService:
    """Service for handling order queries (Read operations) from read replica"""

    def __init__(self, read_db):
        self.read_db = read_db

    def list_orders(self, skip: int, limit: int, status: Optional[str]) -> List[Dict]:
        """List orders from read replica database"""
        if not self.read_db:
            raise ValueError("Read database access is required for queries")

        with self.read_db.cursor() as cursor:
            if status:
                query = """
                    SELECT o.*, array_agg(
                        jsonb_build_object(
                            'sku', oi.sku,
                            'quantity', oi.quantity,
                            'unit_price', oi.unit_price,
                            'sale_price', oi.sale_price
                        )
                    ) as items
                    FROM orders o
                    LEFT JOIN order_items oi ON o.order_id = oi.order_id
                    WHERE o.status = %s
                    GROUP BY o.id, o.order_id, o.customer_id, o.facility_id, o.status,
                             o.total_amount, o.order_datetime, o.eta, o.created_at, o.updated_at
                    ORDER BY o.created_at DESC
                    OFFSET %s LIMIT %s
                """
                cursor.execute(query, (status, skip, limit))
            else:
                query = """
                    SELECT o.*, array_agg(
                        jsonb_build_object(
                            'sku', oi.sku,
                            'quantity', oi.quantity,
                            'unit_price', oi.unit_price,
                            'sale_price', oi.sale_price
                        )
                    ) as items
                    FROM orders o
                    LEFT JOIN order_items oi ON o.order_id = oi.order_id
                    GROUP BY o.id, o.order_id, o.customer_id, o.facility_id, o.status,
                             o.total_amount, o.order_datetime, o.eta, o.created_at, o.updated_at
                    ORDER BY o.created_at DESC
                    OFFSET %s LIMIT %s
                """
                cursor.execute(query, (skip, limit))

            rows = cursor.fetchall()
            return [
                {
                    "id": row[0],
                    "order_id": row[1],
                    "customer_id": row[2],
                    "facility_id": row[3],
                    "status": row[4],
                    "total_amount": float(row[5]),
                    "order_datetime": row[6],
                    "eta": row[7],
                    "created_at": row[8],
                    "updated_at": row[9],
                    "items": [item for item in row[10] if item]
                }
                for row in rows
            ]

    def get_order(self, order_id: str) -> Optional[Dict]:
        """Get single order by order_id from read replica database"""
        if not self.read_db:
            raise ValueError("Read database is required for queries")

        with self.read_db.cursor() as cursor:
            query = """
                SELECT o.*, array_agg(
                    jsonb_build_object(
                        'sku', oi.sku,
                        'quantity', oi.quantity,
                        'unit_price', oi.unit_price,
                        'sale_price', oi.sale_price
                    )
                ) as items
                FROM orders o
                LEFT JOIN order_items oi ON o.order_id = oi.order_id
                WHERE o.order_id = %s
                GROUP BY o.id, o.order_id, o.customer_id, o.facility_id, o.status,
                         o.total_amount, o.order_datetime, o.eta, o.created_at, o.updated_at
            """
            cursor.execute(query, (order_id,))
            row = cursor.fetchone()
            if not row:
                return None
            return {
                "id": row[0],
                "order_id": row[1],
                "customer_id": row[2],
                "facility_id": row[3],
                "status": row[4],
                "total_amount": float(row[5]),
                "order_datetime": row[6],
                "eta": row[7],
                "created_at": row[8],
                "updated_at": row[9],
                "items": [item for item in row[10] if item]
            }

    def get_order_journey(self, order_id: str) -> Optional[List[Dict]]:
        """Get order journey/history from read replica database"""
        if not self.read_db:
            raise ValueError("Read database is required for queries")

        with self.read_db.cursor() as cursor:
            query = """
                SELECT current_status, previous_status, created_at, updated_at
                FROM order_journey
                WHERE order_id = %s
                ORDER BY created_at DESC
            """
            cursor.execute(query, (order_id,))
            rows = cursor.fetchall()
            if not rows:
                return None
            return [
                {
                    "current_status": row[0],
                    "previous_status": row[1],
                    "created_at": row[2],
                    "updated_at": row[3]
                }
                for row in rows
            ]