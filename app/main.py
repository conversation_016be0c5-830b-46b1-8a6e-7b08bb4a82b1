from fastapi import FastAPI, HTTPException, Query
from pydantic import BaseModel, Field
from typing import List, Dict, Optional
from services import OrderCommandService, OrderQueryService
from database import get_write_db, get_read_db, close_all_pools
from infrastructure import close_all_connections, kafka_manager
import logging
from datetime import datetime
from contextlib import asynccontextmanager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialise Sentry (does nothing if SENTRY_DSN is not set)
import sentry_setup  # noqa: F401

@asynccontextmanager
async def lifespan(_: FastAPI):
    logger.info("Starting Order Management System")
    yield
    logger.info("Shutting down Order Management System")
    close_all_pools()
    close_all_connections()
    await kafka_manager.close_producer()

app = FastAPI(
    title="Order Management System",
    description="OMS",
    version="1.0.0",
    lifespan=lifespan
)

class OrderItemCreate(BaseModel):
    sku: str = Field(..., min_length=1, max_length=100)
    quantity: int = Field(..., gt=0)
    unit_price: float = Field(..., gt=0)
    sale_price: float = Field(..., gt=0)

class OrderCreate(BaseModel):
    order_id: str = Field(..., min_length=1, max_length=50)
    customer_id: str = Field(..., min_length=1, max_length=50)
    facility_id: str = Field(..., min_length=1, max_length=50)
    status: str = Field(..., pattern="^(pending|confirmed|delivered|cancelled)$")
    total_amount: float = Field(..., gt=0)
    order_datetime: datetime
    eta: Optional[datetime]
    items: List[OrderItemCreate]

class OrderUpdate(BaseModel):
    customer_id: Optional[str] = Field(None, min_length=1, max_length=50)
    facility_id: Optional[str] = Field(None, min_length=1, max_length=50)
    status: Optional[str] = Field(None, pattern="^(pending|confirmed|delivered|cancelled)$")
    total_amount: Optional[float] = Field(None, gt=0)
    eta: Optional[datetime] = None
    items: Optional[List[OrderItemCreate]] = None

class StockAvailabilityItem(BaseModel):
    sku: str
    requested_quantity: int
    available_quantity: int
    is_available: bool
    shortage: int
    facility: Optional[str] = None
    last_update: Optional[str] = None
    message: str

class StockAvailabilityResponse(BaseModel):
    all_available: bool
    items: List[StockAvailabilityItem]
    total_items_checked: int
    available_items: int
    unavailable_items: int

class OrderResponse(BaseModel):
    success: bool
    message: str
    order_id: str
    eta: Optional[datetime] = None
    inventory_check: Optional[StockAvailabilityResponse] = None
    error_type: Optional[str] = None

class OrderDetailResponse(BaseModel):
    id: int
    order_id: str
    customer_id: str
    facility_id: str
    status: str
    total_amount: float
    order_datetime: datetime
    eta: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    items: List[Dict]

class OrderJourneyResponse(BaseModel):
    current_status: str
    previous_status: Optional[str]
    created_at: datetime
    updated_at: datetime



@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.post("/orders", response_model=OrderResponse)
async def create_order(order: OrderCreate):
    """Create order - Check inventory, then cache in Redis and publish to Kafka"""
    try:
        service = OrderCommandService()
        result = await service.create_order(order.model_dump())

        # If inventory check failed, return 400 with stock details
        if not result.get("success", False):
            if result.get("error_type") == "inventory_service_error":
                raise HTTPException(status_code=503, detail=result.get("message", "Inventory service unavailable"))
            else:
                # Insufficient stock - return 409 Conflict with stock details
                raise HTTPException(
                    status_code=409,
                    detail={
                        "message": result.get("message", "Insufficient inventory"),
                        "inventory_check": result.get("inventory_check")
                    }
                )

        return result

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating order: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.put("/orders/{order_id}")
async def update_order(order_id: str, order_update: OrderUpdate):
    """Update order - Cache in Redis and publish to Kafka"""
    try:
        service = OrderCommandService()
        result = await service.update_order(order_id, order_update.model_dump(exclude_unset=True))
        return result
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating order {order_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/orders", response_model=List[OrderDetailResponse])
async def list_orders(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    status: Optional[str] = Query(None, pattern="^(pending|confirmed|delivered|cancelled)?$")
):
    """List orders - Read from read replica database"""
    async with get_read_db() as read_db:
        try:
            service = OrderQueryService(read_db)
            return service.list_orders(skip, limit, status)
        except Exception as e:
            logger.error(f"Error listing orders: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/orders/{order_id}", response_model=OrderDetailResponse)
async def get_order(order_id: str):
    """Get order details - Read from read replica database"""
    async with get_read_db() as read_db:
        try:
            service = OrderQueryService(read_db)
            order = service.get_order(order_id)
            if not order:
                raise HTTPException(status_code=404, detail="Order not found")
            return order
        except Exception as e:
            logger.error(f"Error getting order {order_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.get("/orders/{order_id}/journey", response_model=List[OrderJourneyResponse])
async def get_order_journey(order_id: str):
    """Get order journey - Read from read replica database"""
    async with get_read_db() as read_db:
        try:
            service = OrderQueryService(read_db)
            journey = service.get_order_journey(order_id)
            if not journey:
                raise HTTPException(status_code=404, detail="Order not found")
            return journey
        except Exception as e:
            logger.error(f"Error getting order journey {order_id}: {e}")
            raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@app.put("/orders/{order_id}/cancel")
async def cancel_order(order_id: str):
    """Cancel order - Direct write to master database (synchronous)"""
    async with get_write_db() as write_db:
        try:
            service = OrderCommandService(write_db)
            result = service.cancel_order(order_id)
            return result
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")