import redis
import json
import logging
from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from typing import Dict, Any, Optional
import os
from dotenv import load_dotenv
import asyncio

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global instances
redis_client = None
kafka_producer = None

class RedisManager:
    """Redis connection and operations manager"""
    
    def __init__(self):
        self.client = None
    
    def get_client(self):
        """Get Redis client instance"""
        global redis_client
        if redis_client is None:
            try:
                redis_client = redis.Redis(
                    host=os.getenv("REDIS_HOST", "redis"),
                    port=int(os.getenv("REDIS_PORT", 6379)),
                    db=int(os.getenv("REDIS_DB", 0)),
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True,
                    health_check_interval=30
                )
                # Test connection
                redis_client.ping()
                logger.info("Redis connection established")
            except Exception as e:
                logger.error(f"Failed to connect to Redis: {e}")
                raise
        return redis_client
    
    def set_order_cache(self, order_id: str, order_data: Dict[str, Any], ttl: int = 3600):
        """Cache order data in Redis"""
        try:
            client = self.get_client()
            client.setex(f"order:{order_id}", ttl, json.dumps(order_data, default=str))
            logger.info(f"Order {order_id} cached successfully")
        except Exception as e:
            logger.error(f"Failed to cache order {order_id}: {e}")
            raise
    
    def get_order_cache(self, order_id: str) -> Optional[Dict[str, Any]]:
        """Get cached order data from Redis"""
        try:
            client = self.get_client()
            cached_data = client.get(f"order:{order_id}")
            if cached_data:
                return json.loads(cached_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get cached order {order_id}: {e}")
            return None
    
    def delete_order_cache(self, order_id: str):
        """Delete cached order data from Redis"""
        try:
            client = self.get_client()
            client.delete(f"order:{order_id}")
            logger.info(f"Order {order_id} cache deleted")
        except Exception as e:
            logger.error(f"Failed to delete cached order {order_id}: {e}")

class KafkaManager:
    """Kafka producer and consumer manager"""
    
    def __init__(self):
        self.producer = None
        self.bootstrap_servers = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "kafka:9092")
    
    async def get_producer(self):
        """Get Kafka producer instance"""
        global kafka_producer
        if kafka_producer is None:
            try:
                kafka_producer = AIOKafkaProducer(
                    bootstrap_servers=self.bootstrap_servers,
                    value_serializer=lambda v: json.dumps(v, default=str).encode('utf-8'),
                    key_serializer=lambda k: k.encode('utf-8') if k else None,
                    retry_backoff_ms=1000,
                    request_timeout_ms=30000,
                    acks='all'
                )
                await kafka_producer.start()
                logger.info("Kafka producer started")
            except Exception as e:
                logger.error(f"Failed to start Kafka producer: {e}")
                raise
        return kafka_producer
    
    async def publish_order_event(self, event_type: str, order_data: Dict[str, Any]):
        """Publish order event to Kafka"""
        try:
            producer = await self.get_producer()
            topic = os.getenv("KAFKA_ORDER_TOPIC", "order-events")
            
            event = {
                "event_type": event_type,
                "timestamp": str(asyncio.get_event_loop().time()),
                "data": order_data
            }
            
            await producer.send(
                topic,
                value=event,
                key=order_data.get("order_id")
            )
            logger.info(f"Published {event_type} event for order {order_data.get('order_id')}")
        except Exception as e:
            logger.error(f"Failed to publish order event: {e}")
            raise
    
    async def create_consumer(self, topic: str, group_id: str):
        """Create Kafka consumer"""
        try:
            consumer = AIOKafkaConsumer(
                topic,
                bootstrap_servers=self.bootstrap_servers,
                group_id=group_id,
                value_deserializer=lambda m: json.loads(m.decode('utf-8')),
                key_deserializer=lambda k: k.decode('utf-8') if k else None,
                auto_offset_reset='earliest',
                enable_auto_commit=True,
                auto_commit_interval_ms=1000
            )
            await consumer.start()
            logger.info(f"Kafka consumer started for topic {topic}")
            return consumer
        except Exception as e:
            logger.error(f"Failed to create Kafka consumer: {e}")
            raise
    
    async def close_producer(self):
        """Close Kafka producer"""
        global kafka_producer
        if kafka_producer:
            try:
                await kafka_producer.stop()
                kafka_producer = None
                logger.info("Kafka producer closed")
            except Exception as e:
                logger.error(f"Error closing Kafka producer: {e}")

def close_all_connections():
    """Close all Redis and Kafka connections"""
    global redis_client
    
    # Close Redis connection
    if redis_client:
        try:
            redis_client.close()
            redis_client = None
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")

# Global instances for easy access
redis_manager = RedisManager()
kafka_manager = KafkaManager()
