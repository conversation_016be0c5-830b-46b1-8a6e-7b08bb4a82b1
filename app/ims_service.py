import httpx
import os
import logging
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

class IMSService:
    """Service for interacting with Inventory Management System (IMS)"""
    
    def __init__(self):
        self.base_url = os.getenv("IMS_BASE_URL", "https://ims.sangaraju.in")
        self.timeout = 30.0  # 30 seconds timeout
    
    async def check_stock_availability(self, facility_id: str, items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Check stock availability for multiple SKUs in a facility
        
        Args:
            facility_id: The facility/warehouse ID
            items: List of items with sku and quantity
            
        Returns:
            Dict with availability status and stock details
        """
        try:
            # Extract unique SKUs from items
            skus = list(set([item['sku'] for item in items]))
            
            # Build query parameters
            sku_param = ','.join(skus)
            
            # Make request to IMS
            url = f"{self.base_url}/api/stock/multi/"
            params = {
                'facility': facility_id,
                'skus': sku_param
            }
            
            logger.info(f"Checking stock for facility {facility_id}, SKUs: {sku_param}")
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(url, params=params)
                response.raise_for_status()
                
                ims_data = response.json()
                
                if not ims_data.get('success', False):
                    raise Exception(f"IMS API returned error: {ims_data}")
                
                # Process the response
                return self._process_stock_response(items, ims_data.get('data', []))
                
        except httpx.TimeoutException:
            logger.error(f"Timeout while checking stock for facility {facility_id}")
            raise Exception("Inventory service timeout - please try again")
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error while checking stock: {e.response.status_code} - {e.response.text}")
            raise Exception(f"Inventory service error: {e.response.status_code}")
        except Exception as e:
            logger.error(f"Error checking stock availability: {e}")
            raise Exception(f"Failed to check inventory: {str(e)}")
    
    def _process_stock_response(self, requested_items: List[Dict[str, Any]], stock_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process IMS response and check if all items are available
        
        Args:
            requested_items: Items requested in the order
            stock_data: Stock data from IMS
            
        Returns:
            Dict with availability status and details
        """
        # Create a mapping of SKU to stock info
        stock_map = {item['sku_code']: item for item in stock_data}
        
        availability_results = []
        all_available = True
        
        for item in requested_items:
            sku = item['sku']
            requested_qty = item['quantity']
            
            stock_info = stock_map.get(sku)
            
            if not stock_info:
                # SKU not found in inventory
                availability_results.append({
                    'sku': sku,
                    'requested_quantity': requested_qty,
                    'available_quantity': 0,
                    'is_available': False,
                    'shortage': requested_qty,
                    'message': 'SKU not found in inventory'
                })
                all_available = False
            else:
                available_qty = stock_info.get('available_quantity', 0)
                is_available = available_qty >= requested_qty
                shortage = max(0, requested_qty - available_qty)
                
                availability_results.append({
                    'sku': sku,
                    'requested_quantity': requested_qty,
                    'available_quantity': available_qty,
                    'is_available': is_available,
                    'shortage': shortage,
                    'facility': stock_info.get('facility'),
                    'last_update': stock_info.get('last_update', ''),
                    'message': 'Available' if is_available else f'Insufficient stock (short by {shortage})'
                })
                
                if not is_available:
                    all_available = False
        
        return {
            'all_available': all_available,
            'items': availability_results,
            'total_items_checked': len(requested_items),
            'available_items': len([item for item in availability_results if item['is_available']]),
            'unavailable_items': len([item for item in availability_results if not item['is_available']])
        }

# Global instance
ims_service = IMSService()
