import psycopg
from psycopg import sql
import os
from dotenv import load_dotenv

load_dotenv()

def setup_database(dsn, db_name):
    try:
        with psycopg.connect(dsn, autocommit=True) as conn:
            with conn.cursor() as cursor:
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS orders (
                        id SERIAL PRIMARY KEY,
                        order_id VARCHAR(50) UNIQUE NOT NULL,
                        customer_id VARCHAR(50) NOT NULL,
                        facility_id VARCHAR(50) NOT NULL,
                        status VARCHAR(20) NOT NULL CHECK (
                            status IN ('pending', 'confirmed', 'delivered', 'cancelled')
                        ),
                        total_amount DECIMAL(10,2) NOT NULL,
                        order_datetime TIMESTAMP NOT NULL,
                        eta TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                """)

                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS order_items (
                        id SERIAL PRIMARY KEY,
                        order_id VARCHAR(50) REFERENCES orders(order_id),
                        sku VARCHAR(100) NOT NULL,
                        quantity INTEGER NOT NULL CHECK (quantity > 0),
                        unit_price DECIMAL(10,2) NOT NULL,
                        sale_price DECIMAL(10,2) NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                """)

                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS order_journey (
                        id SERIAL PRIMARY KEY,
                        order_id VARCHAR(50) REFERENCES orders(order_id),
                        current_status VARCHAR(20) NOT NULL,
                        previous_status VARCHAR(20),
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    );
                """)

                print(f"Database schema set up successfully for {db_name}")
    except Exception as e:
        print(f"Error setting up {db_name}: {str(e)}")

if __name__ == "__main__":
    # Setup schema on the master database
    # Both WRITE_DATABASE_URL and READ_DATABASE_URL point to the same database currently
    # In future, if you add a read replica, you can modify READ_DATABASE_URL
    setup_database(os.getenv("WRITE_DATABASE_URL"), "master_db")

    # Verify read database connection (currently same as write database)
    read_db_url = os.getenv("READ_DATABASE_URL")
    write_db_url = os.getenv("WRITE_DATABASE_URL")

    if read_db_url != write_db_url:
        print("Setting up read replica database...")
        setup_database(read_db_url, "read_replica_db")
        print("Schema setup complete for both master and read replica databases.")
    else:
        print("Using single master database for both read and write operations.")
        print("Schema setup complete for master database.")