#!/usr/bin/env python3
"""
Database connection test script for PostgreSQL primary-replica setup
"""
import os
import psycopg
import time
from dotenv import load_dotenv

load_dotenv()

def test_connection(db_url, db_name):
    """Test database connection and basic operations"""
    try:
        print(f"\n=== Testing {db_name} ===")
        print(f"Connection URL: {db_url}")
        
        with psycopg.connect(db_url) as conn:
            with conn.cursor() as cursor:
                # Test basic connection
                cursor.execute("SELECT version();")
                version = cursor.fetchone()[0]
                print(f"✓ Connection successful")
                print(f"  PostgreSQL version: {version}")
                
                # Test database name
                cursor.execute("SELECT current_database();")
                current_db = cursor.fetchone()[0]
                print(f"  Current database: {current_db}")
                
                # Test user
                cursor.execute("SELECT current_user;")
                current_user = cursor.fetchone()[0]
                print(f"  Current user: {current_user}")
                
                # Test if this is a replica
                cursor.execute("SELECT pg_is_in_recovery();")
                is_replica = cursor.fetchone()[0]
                print(f"  Is replica: {is_replica}")
                
                if not is_replica:
                    # Primary database tests
                    print("  Running primary database tests...")
                    
                    # Test write operation
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS test_table (
                            id SERIAL PRIMARY KEY,
                            test_data VARCHAR(100),
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        );
                    """)
                    
                    cursor.execute("""
                        INSERT INTO test_table (test_data) 
                        VALUES ('Test data from primary') 
                        RETURNING id;
                    """)
                    test_id = cursor.fetchone()[0]
                    print(f"  ✓ Write test successful, inserted record with ID: {test_id}")
                    
                    # Check replication slots
                    cursor.execute("SELECT slot_name, active FROM pg_replication_slots;")
                    slots = cursor.fetchall()
                    print(f"  Replication slots: {slots}")
                    
                else:
                    # Replica database tests
                    print("  Running replica database tests...")
                    
                    # Test read operation
                    cursor.execute("SELECT COUNT(*) FROM test_table;")
                    count = cursor.fetchone()[0]
                    print(f"  ✓ Read test successful, found {count} records")
                    
                    # Check replication status
                    cursor.execute("SELECT pg_last_wal_receive_lsn(), pg_last_wal_replay_lsn();")
                    lsns = cursor.fetchone()
                    print(f"  Last WAL receive LSN: {lsns[0]}")
                    print(f"  Last WAL replay LSN: {lsns[1]}")
                
                conn.commit()
                print(f"✓ {db_name} tests completed successfully")
                
    except Exception as e:
        print(f"✗ {db_name} connection failed: {str(e)}")
        return False
    
    return True

def test_replication():
    """Test replication between primary and replica"""
    print("\n=== Testing Replication ===")
    
    write_url = os.getenv("WRITE_DATABASE_URL")
    read_url = os.getenv("READ_DATABASE_URL")
    
    try:
        # Insert data on primary
        with psycopg.connect(write_url) as write_conn:
            with write_conn.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO test_table (test_data) 
                    VALUES ('Replication test data') 
                    RETURNING id, created_at;
                """)
                test_record = cursor.fetchone()
                write_conn.commit()
                print(f"✓ Inserted test record on primary: ID={test_record[0]}, Time={test_record[1]}")
        
        # Wait a moment for replication
        print("  Waiting 3 seconds for replication...")
        time.sleep(3)
        
        # Check if data appears on replica
        with psycopg.connect(read_url) as read_conn:
            with read_conn.cursor() as cursor:
                cursor.execute("""
                    SELECT id, test_data, created_at 
                    FROM test_table 
                    WHERE id = %s;
                """, (test_record[0],))
                replica_record = cursor.fetchone()
                
                if replica_record:
                    print(f"✓ Replication successful! Found record on replica: {replica_record}")
                else:
                    print("✗ Replication failed - record not found on replica")
                    return False
        
        return True
        
    except Exception as e:
        print(f"✗ Replication test failed: {str(e)}")
        return False

def main():
    """Main test function"""
    print("PostgreSQL Primary-Replica Database Connection Test")
    print("=" * 50)
    
    write_url = os.getenv("WRITE_DATABASE_URL")
    read_url = os.getenv("READ_DATABASE_URL")
    
    if not write_url or not read_url:
        print("✗ Database URLs not found in environment variables")
        print("  Make sure WRITE_DATABASE_URL and READ_DATABASE_URL are set")
        return
    
    # Test primary database
    primary_ok = test_connection(write_url, "Primary Database")
    
    # Test replica database
    replica_ok = test_connection(read_url, "Replica Database")
    
    # Test replication if both databases are working
    if primary_ok and replica_ok:
        replication_ok = test_replication()
    else:
        replication_ok = False
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Primary Database: {'✓ PASS' if primary_ok else '✗ FAIL'}")
    print(f"Replica Database: {'✓ PASS' if replica_ok else '✗ FAIL'}")
    print(f"Replication: {'✓ PASS' if replication_ok else '✗ FAIL'}")
    
    if primary_ok and replica_ok and replication_ok:
        print("\n🎉 All tests passed! Your PostgreSQL setup is working correctly.")
    else:
        print("\n❌ Some tests failed. Please check the configuration.")

if __name__ == "__main__":
    main()
