# Connection settings
listen_addresses = '*'
port = 5432

# Replica settings
hot_standby = on
max_standby_streaming_delay = 30s
max_standby_archive_delay = 30s
hot_standby_feedback = on

# Performance settings
shared_buffers = 256MB
effective_cache_size = 1GB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Logging
log_destination = 'stderr'
logging_collector = on
log_statement = 'all'
log_min_duration_statement = 1000

# Recovery settings
primary_conninfo = 'host=postgres-db port=5432 user=write_user password=write_pass'
primary_slot_name = 'replica_slot'
