# PostgreSQL Primary-Replica Setup - Execution Flow

## Prerequisites
- Dock<PERSON> and Docker Compose installed
- Python 3.11+ with pip
- All configuration files in place

## Step-by-Step Execution Flow

### 1. Clean Environment
```bash
# Stop and remove all containers and volumes
docker-compose down -v

# Remove any orphaned containers
docker system prune -f
```

### 2. Verify Configuration Files
Ensure these files exist and are properly configured:

**Required Files:**
- `.env` - Environment variables
- `docker-compose.yml` - Container orchestration
- `primary/init.sql` - Database initialization
- `primary/postgres.conf` - Primary DB config
- `primary/pg_hba.conf` - Authentication config
- `replica/postgres.conf` - Replica DB config
- `app/test_db_connections.py` - Testing script

### 3. Build and Start Services
```bash
# Build and start all services
docker-compose up --build -d

# Check container status
docker-compose ps
```

**Expected Output:**
- All containers should be "Up" or "Healthy"
- Primary DB: `oms-postgres` on port 5432
- Replica DB: `oms-postgres-replica` on port 5433

### 4. Monitor Startup Process
```bash
# Watch primary database logs
docker-compose logs -f postgres-db

# Watch replica database logs (in another terminal)
docker-compose logs -f postgres-read-db
```

**What to Look For:**
- Primary: "database system is ready to accept connections"
- Replica: "started streaming WAL from primary"

### 5. Test Database Connections

#### Option A: From Host Machine
```bash
# Install Python dependencies
pip3 install psycopg[binary] python-dotenv

# Run connection test
python3 app/test_db_connections.py
```

#### Option B: From Inside Container
```bash
# Enter the API container
docker exec -it oms-api bash

# Run the test script
python test_db_connections.py
```

### 6. Expected Test Results
```
PostgreSQL Primary-Replica Database Connection Test
==================================================

=== Testing Primary Database ===
✓ Connection successful
  PostgreSQL version: PostgreSQL 15.13...
  Current database: oms_db
  Current user: write_user
  Is replica: False
  ✓ Write test successful
  Replication slots: [('replica_slot', True)]

=== Testing Replica Database ===
✓ Connection successful
  PostgreSQL version: PostgreSQL 15.13...
  Current database: oms_db
  Current user: read_user
  Is replica: True
  ✓ Read test successful
  ✓ Write rejection test successful (read-only)

=== Testing Replication ===
✓ Replication working correctly
  Records replicated: 1

==================================================
TEST SUMMARY
==================================================
Primary Database: ✓ PASS
Replica Database: ✓ PASS
Replication: ✓ PASS

🎉 All tests passed!
```

## Troubleshooting Common Issues

### Issue 1: Primary Database Fails to Start
**Symptoms:** `oms-postgres exited (3)`

**Solutions:**
1. Check logs: `docker-compose logs postgres-db`
2. Look for SQL syntax errors in init.sql
3. Verify environment variables in .env file
4. Clean volumes: `docker-compose down -v`

### Issue 2: Replica Database Keeps Restarting
**Symptoms:** `Restarting (1) Less than a second ago`

**Solutions:**
1. Check replica logs: `docker-compose logs postgres-read-db`
2. Ensure primary is healthy before replica starts
3. Verify replication slot exists on primary
4. Check replica configuration files

### Issue 3: Connection Refused Errors
**Symptoms:** `connection refused` or `Name or service not known`

**Solutions:**
1. Verify containers are running: `docker-compose ps`
2. Check port mappings in docker-compose.yml
3. Ensure network connectivity between containers
4. Wait for health checks to pass

### Issue 4: Authentication Failures
**Symptoms:** `password authentication failed`

**Solutions:**
1. Verify credentials in .env file
2. Check pg_hba.conf configuration
3. Ensure users are created in init.sql
4. Restart containers after config changes

## Manual Verification Commands

### Check Replication Status
```bash
# Connect to primary database
docker exec -it oms-postgres psql -U write_user -d oms_db

# Check replication slots
SELECT slot_name, active FROM pg_replication_slots;

# Check connected replicas
SELECT client_addr, state FROM pg_stat_replication;
```

### Test Write/Read Operations
```bash
# Write to primary
docker exec -it oms-postgres psql -U write_user -d oms_db -c "CREATE TABLE test_table (id SERIAL, data TEXT); INSERT INTO test_table (data) VALUES ('test data');"

# Read from replica
docker exec -it oms-postgres-replica psql -U read_user -d oms_db -c "SELECT * FROM test_table;"
```

## Environment Variables Reference
```bash
# Database Configuration
POSTGRES_DB=oms_db
WRITE_DB_USER=write_user
WRITE_DB_PASSWORD=write_pass
READ_DB_USER=read_user
READ_DB_PASSWORD=read_pass

# Application URLs
WRITE_DATABASE_URL=***************************************************/oms_db
READ_DATABASE_URL=******************************************************/oms_db
```

## Success Indicators
- ✅ All containers running and healthy
- ✅ Primary database accepts writes
- ✅ Replica database rejects writes (read-only)
- ✅ Data replicates from primary to replica
- ✅ Application can connect to both databases
- ✅ Test script passes all checks
