FROM python:3.11-slim

WORKDIR /app

# Install PostgreSQL client libraries and tools
RUN apt-get update && apt-get install -y \
    libpq-dev \
    postgresql-client \
    gcc \
    netcat-openbsd \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY app/ .

ENV PYTHONUNBUFFERED=1

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
